import { useEffect, useState } from 'react';
import { groupBy } from 'lodash';

import { iconToCompanyName, LLM_MODEL_DEFAULT } from '@/constants';
import loadIcon from '@/helpers/loadIcons';
import useDanteApi from '@/hooks/useDanteApi';
import { getLLMModels } from '@/services/model.service';
import {
  Listbox,
  ListboxButton,
  ListboxOption,
  ListboxOptions,
} from '@headlessui/react';
import { updateChatbotLLMModel } from '@/services/chatbot.service';
import ChevronDownIcon from '../Global/Icons/ChevronDownIcon';

import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import OpenAiLogoIcon from '../Global/Icons/OpenAiLogoIcon';
import useToast from '@/hooks/useToast';
import { useUserStore } from '@/stores/user/userStore';
import { useParams } from 'react-router-dom';
import featureCheck, { checkFeatureAvailability } from '@/helpers/tier/featureCheck';

const LLMSelector = () => {
  const { user } = useUserStore();
  const params = useParams();
  const selectedChatbot = useChatbotStore((state) => state.selectedChatbot);
  const [selectedLLM, setSelectedLLM] = useState(selectedChatbot?.last_model_used ?? LLM_MODEL_DEFAULT);
  const setSelectedLLModel = useChatbotStore(
    (state) => state.setSelectedLLModel
  );
  const [icons, setIcons] = useState({});

  const [modelsByCompany, setModelsByCompany] = useState({});

  const { data: models } = useDanteApi(getLLMModels, [], {}, {
    tier_type: user.tier_type,
  });

  const { addWarningToast } = useToast();

  const handleChangeModel = async (model) => {
   
    if(model.value === 'cohere' && !featureCheck('cohere_model')) {
      return;
    }

    if(model.value.includes('claude') && !featureCheck('claude_model')) {
      return;
    }

    if (!model.available) {
      addWarningToast({
        message:
          'This model is not available for your plan. Please upgrade your plan to use this model.',
      });
      return;
    }
    setSelectedLLM({ ...model, Icon: icons[model.Logo] });
    setSelectedLLModel(model);

    try{
        await updateChatbotLLMModel({
            kb_id: params?.id,
            llmModel: model.value
        });
    } catch (e) {
        console.error('Failed to update chatbot LLM model', e);
    }
  };

  useEffect(() => {
    const loadModels = async () => {
      const groupedModels = groupBy(models, 'Logo');

      const iconComponents = {};

      for (const company of Object.keys(groupedModels)) {
        try {
          const iconCompany = await loadIcon(company);
          iconComponents[company] = iconCompany;
        } catch (e) {
          console.error(`Failed to load icon ${company}`, e);
        }
      }

      setIcons(iconComponents);
      setModelsByCompany(groupedModels);
    };
    if (models?.length) {
      loadModels();
    }
  }, [models]);

  useEffect(() => {
    setSelectedLLM(selectedChatbot?.last_model_used ?? LLM_MODEL_DEFAULT);
  }, [selectedChatbot?.last_model_used]);

  return (
    <Listbox
      value={selectedLLM}
      onChange={handleChangeModel}
      className="group"
      as="div"
    >
      <ListboxButton
        className="flex items-center justify-between gap-size1 text-xs px-size1 py-size0 rounded-size1 transition-colors duration-150 hover:bg-grey-5"
        style={{
          backgroundColor: 'var(--dt-color-surface-100)',
          color: 'var(--dt-color-element-100)'
        }}
      >
        <div className="flex items-center justify-center h-full">
          {selectedLLM.Icon ? (
            <selectedLLM.Icon className="size-4" />
          ) : (
            <OpenAiLogoIcon className="size-4" />
          )}
        </div>
        <div
          className="flex items-center justify-center font-medium"
          style={{ color: 'var(--dt-color-element-100)' }}
        >
          {selectedLLM.label}
        </div>
        <ChevronDownIcon
          className="transition-transform group pointer-events-none size-3 group-data-[open]:rotate-180"
          style={{ fill: 'var(--dt-color-element-100)' }}
          aria-hidden="true"
        />
      </ListboxButton>
      <ListboxOptions
        anchor={{
          to: 'top start',
          gap: 8,
        }}
        transition
        className="flex flex-col bg-white gap-size2 py-size2 rounded-size2 border border-grey-10 origin-bottom transition duration-200 ease-out data-[closed]:scale-95 data-[closed]:opacity-0 z-[999] !max-h-[500px] overflow-y-auto scrollbar"
        style={{
          backgroundColor: 'var(--dt-color-surface-100)',
          borderColor: 'var(--dt-color-element-10)'
        }}
      >
        {Object.keys(modelsByCompany)?.map((company) => {
          const Icon = icons[company];

          return (
            <div key={company} className="flex flex-col gap-size0">
              <span
                className="text-xs font-medium px-size2"
                style={{ color: 'var(--dt-color-element-50)' }}
              >
                {iconToCompanyName[company]}
              </span>
              {modelsByCompany[company].map((model) => {
                const isModelDisabled = !model.available ||
                  (model.value === 'cohere' && !checkFeatureAvailability('cohere_model', user.tier_type)) ||
                  (model.value.includes('claude') && !checkFeatureAvailability('claude_model', user.tier_type));

                return (
                  <ListboxOption
                    as="button"
                    key={model.value}
                    value={model}
                    className={'flex items-center gap-size1 text-sm py-size0 px-size2 cursor-pointer data-[disabled]:opacity-50 data-[disabled]:cursor-default transition-all duration-150 data-[selected]:font-medium rounded-size0'}
                    style={{
                      color: 'var(--dt-color-element-100)',
                      '--hover-bg': 'var(--dt-color-element-5)',
                      '--selected-bg': 'var(--dt-color-brand-10)',
                      '--selected-color': 'var(--dt-color-brand-100)',
                      '--disabled-hover-bg': 'var(--dt-color-surface-100)',
                      backgroundColor: selectedLLM.value === model.value ? 'var(--dt-color-brand-10)' : 'transparent',
                      border: selectedLLM.value === model.value ? '1px solid var(--dt-color-brand-40)' : '1px solid transparent'
                    }}
                    onMouseEnter={(e) => {
                      const isSelected = selectedLLM.value === model.value;
                      if (!isModelDisabled && !isSelected) {
                        e.target.style.backgroundColor = 'var(--dt-color-element-5)';
                      }
                    }}
                    onMouseLeave={(e) => {
                      const isSelected = selectedLLM.value === model.value;
                      if (!isModelDisabled) {
                        if (isSelected) {
                          e.target.style.backgroundColor = 'var(--dt-color-brand-10)';
                        } else {
                          e.target.style.backgroundColor = 'transparent';
                        }
                      } else {
                        e.target.style.backgroundColor = 'var(--dt-color-surface-100)';
                      }
                    }}
                    disabled={isModelDisabled}
                  >
                      {Icon && <Icon className="size-4" />}
                      <div className='flex flex-col items-start flex-1'>
                        <span
                          style={{
                            color: selectedLLM.value === model.value
                              ? 'var(--dt-color-brand-100)'
                              : 'var(--dt-color-element-100)'
                          }}
                        >
                          {model.label}
                        </span>
                        <span
                          className='text-xs leading-none'
                          style={{
                            color: selectedLLM.value === model.value
                              ? 'var(--dt-color-brand-60)'
                              : 'var(--dt-color-element-50)'
                          }}
                        >
                          {model.credits} credits per response
                        </span>
                      </div>
                      {selectedLLM.value === model.value && (
                        <div
                          className="flex items-center justify-center size-4 rounded-full"
                          style={{ backgroundColor: 'var(--dt-color-brand-100)' }}
                        >
                          <svg
                            className="size-2.5"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                            style={{ color: 'var(--dt-color-surface-100)' }}
                          >
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                      )}
                  </ListboxOption>
                );
              })}
            </div>
          );
        })}
      </ListboxOptions>
    </Listbox>
  );
};

export default LLMSelector;
